<?php
declare(strict_types=1);

namespace App\Service;

use App\Model\Table\GeneralUsersTable;
use App\Model\Table\SwbUsersTable;
use App\Model\Table\MakerUsersTable;
use App\Model\Entity\GeneralUser;
use App\Model\Entity\SwbUser;
use App\Model\Entity\MakerUser;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;

/**
 * 統合認証サービス
 * 
 * Kuroco認証と新システム認証を統合的に処理するサービスクラス
 */
class AuthenticationService
{
    // ユーザータイプの定数
    const USER_TYPE_GENERAL = 'general';
    const USER_TYPE_SWB = 'swb';
    const USER_TYPE_MAKER = 'maker';

    private GeneralUsersTable $generalUsersTable;
    private SwbUsersTable $swbUsersTable;
    private MakerUsersTable $makerUsersTable;
    private DefaultPasswordHasher $passwordHasher;

    public function __construct()
    {
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->swbUsersTable = TableRegistry::getTableLocator()->get('SwbUsers');
        $this->makerUsersTable = TableRegistry::getTableLocator()->get('MakerUsers');
        $this->passwordHasher = new DefaultPasswordHasher();
    }

    /**
     * メールアドレスとパスワードでユーザー認証
     * 
     * @param string $email メールアドレス
     * @param string $password パスワード
     * @param string $userType ユーザータイプ
     * @return GeneralUser|SwbUser|MakerUser|null 認証されたユーザーエンティティ
     */
    public function authenticate(string $email, string $password, string $userType)
    {
        switch ($userType) {
            case self::USER_TYPE_GENERAL:
                return $this->authenticateGeneralUser($email, $password);
            case self::USER_TYPE_SWB:
                return $this->authenticateSwbUser($email, $password);
            case self::USER_TYPE_MAKER:
                return $this->authenticateMakerUser($email, $password);
            default:
                Log::error("Invalid user type: {$userType}");
                return null;
        }
    }

    /**
     * 一般ユーザーの認証
     */
    private function authenticateGeneralUser(string $email, string $password): ?GeneralUser
    {
        Log::debug("AuthenticationService: Attempting general user authentication for: {$email}");

        $user = $this->generalUsersTable->find()
            ->contain(['UserProfiles', 'UserSurveys', 'UserTokens'])
            ->where(['email' => $email])
            ->first();

        if (!$user) {
            Log::debug("AuthenticationService: General user not found: {$email}");
            return null;
        }

        // Kurocoユーザーの場合はKuroco認証にリダイレクト
        /** @var GeneralUser $user */
        if ($user->isKurocoUser()) {
            Log::info("AuthenticationService: Kuroco user detected (password is null): {$email}");
            return null;
        }

        // 新システムユーザーのパスワード認証
        if ($this->passwordHasher->check($password, $user->password)) {
            Log::info("AuthenticationService: New system authentication successful for: {$email}");
            return $user;
        }

        Log::debug("AuthenticationService: Password verification failed for: {$email}");
        return null;
    }

    /**
     * SWB管理者ユーザーの認証
     */
    private function authenticateSwbUser(string $email, string $password): ?SwbUser
    {
        Log::debug("AuthenticationService: Attempting SWB user authentication for: {$email}");

        $user = $this->swbUsersTable->find()->where(['email' => $email])->first();

        if (!$user) {
            Log::debug("AuthenticationService: SWB user not found: {$email}");
            return null;
        }

        // SWB管理者は完全に新システムに移行済み
        // 新システムユーザーのパスワード認証
        if ($this->passwordHasher->check($password, $user->password)) {
            Log::info("AuthenticationService: SWB user authentication successful for: {$email}");
            return $user;
        }

        Log::debug("AuthenticationService: SWB user password verification failed for: {$email}");
        return null;
    }

    /**
     * メーカーユーザーの認証
     */
    private function authenticateMakerUser(string $email, string $password): ?MakerUser
    {
        Log::debug("AuthenticationService: Attempting maker user authentication for: {$email}");

        $user = $this->makerUsersTable->find()->where(['email' => $email])->first();

        if (!$user) {
            Log::debug("AuthenticationService: Maker user not found: {$email}");
            return null;
        }

        // メーカーユーザーは完全に新システムに移行済み
        // 新システムユーザーのパスワード認証
        if ($this->passwordHasher->check($password, $user->password)) {
            Log::info("AuthenticationService: Maker user authentication successful for: {$email}");
            return $user;
        }

        Log::debug("AuthenticationService: Maker user password verification failed for: {$email}");
        return null;
    }

    /**
     * ユーザーがKurocoユーザーかどうかを判定
     */
    public function isKurocoUser(string $email, string $userType): bool
    {
        Log::debug("AuthenticationService: Checking if user is Kuroco user: {$email}, type: {$userType}");

        switch ($userType) {
            case self::USER_TYPE_GENERAL:
                $user = $this->generalUsersTable->find()->where(['email' => $email])->first();
                break;
            case self::USER_TYPE_SWB:
                $user = $this->swbUsersTable->find()->where(['email' => $email])->first();
                break;
            case self::USER_TYPE_MAKER:
                $user = $this->makerUsersTable->find()->where(['email' => $email])->first();
                break;
            default:
                Log::warning("AuthenticationService: Invalid user type: {$userType}");
                return false;
        }

        $isKuroco = $user ? $user->isKurocoUser() : false;
        Log::debug("AuthenticationService: User {$email} is Kuroco user: " . ($isKuroco ? 'true' : 'false'));
        return $isKuroco;
    }

    /**
     * パスワードの妥当性をチェック
     */
    public function validatePassword(string $password): array
    {
        $errors = [];

        if (strlen($password) < 8) {
            $errors[] = 'パスワードは8文字以上で入力してください';
        }

        if (!preg_match('/[A-Za-z]/', $password)) {
            $errors[] = 'パスワードには英字を含めてください';
        }

        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'パスワードには数字を含めてください';
        }

        return $errors;
    }

    /**
     * パスワードをハッシュ化
     */
    public function hashPassword(string $password): string
    {
        return $this->passwordHasher->hash($password);
    }
}



